from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.middleware.gzip import GZipMiddleware

from pyxis.utils.app_factory import create_app
from pyxis_q.pyxis_q import PyxisQ
from src.router.app import router as profits_router
from src.utils import middleware

from src.utils.resource_loader import CONFIG, RDB_POOLS, CONFIG_LOADER, LOGGER, PYXIS_Q_TABLE
from src.utils.response_generator import response_generator

app = create_app(
    infra=CONFIG.deploy.infra,
    enable_doc=CONFIG.deploy.enable_doc,
    service_name=CONFIG.deploy.service_name,
    uri_prefix=CONFIG.deploy.uri_prefix
)


# 添加启动、关闭处理函数，释放资源
def startup():
    for pool in RDB_POOLS.values():
        pool.reflect()
    data_source_info = CONFIG_LOADER.client.get_rdb_config_by_name(
        datasource_name=CONFIG.rdb["default"].datasource_name,
        tables=set(),
        schema=CONFIG.rdb["default"].schema
    )
    consumer = PyxisQ(
        **data_source_info,
        table=PYXIS_Q_TABLE,
        num_workers=2,
        task_group={"default"},
        batch_size=5,
        max_task_per_child=200,
        conn_recycle_interval_sec=60 * 30,
        logger=LOGGER
    )
    consumer.run()


def shutdown():
    for pool in RDB_POOLS.values():
        pool.release()


app.add_event_handler("startup", startup)
app.add_event_handler("shutdown", shutdown)


# 挂载子服务
app.include_router(profits_router)

# 添加中间件函数、异常处理函数
app.add_exception_handler(RequestValidationError, middleware.validation_error_handler)
app.add_exception_handler(ValidationError, middleware.validation_error_handler)
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(middleware.AuthenticationMiddleware)
app.add_middleware(middleware.RequestMiddleware)
app.add_middleware(middleware.ExceptionMiddleware)
app.add_middleware(middleware.TimingMiddleware)
app.add_middleware(middleware.AuditLoggingMiddleware)


@app.get(f"{CONFIG.deploy.service_name}/health", include_in_schema=False)
def health_check(
        request: Request,
):
    """
    健康检测
    """
    return response_generator(
        status.HTTP_200_OK, middleware.AppBizStatus.SUCCESS, middleware.AppBizStatusMsg.HEALTH_TEST, list()
    )
